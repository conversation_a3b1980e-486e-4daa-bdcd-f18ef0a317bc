import { useEffect, useState } from "react";
import axios from "axios";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import { Link } from "react-router-dom";

export function Navbar() {
  const [professions, setProfessions] = useState([]);

  useEffect(() => {
    const fetchProfessions = async () => {
      try {
        const res = await axios.get(
          "http://localhost:3000/api/admin/professions"
        );
        console.log("Professions response:", res.data); // Debug log
        setProfessions(res.data.data || []); // make sure the response is shaped as expected
      } catch (error) {
        console.error("Error fetching professions:", error);
        // Set empty array on error to prevent crashes
        setProfessions([]);
      }
    };

    fetchProfessions();
  }, []);

  // Debug log to see what data we have
  console.log("Professions in render:", professions);

  return (
    <NavigationMenu viewport={false}>
      <NavigationMenuList>
        {professions.length === 0 ? (
          <NavigationMenuItem>
            <span className="text-muted-foreground">
              Loading professions...
            </span>
          </NavigationMenuItem>
        ) : (
          professions.map((profession) => (
            <NavigationMenuItem key={profession._id}>
              <NavigationMenuTrigger>
                <Link to={`/profession/${profession._id}`}>
                  {profession.professionTitle}
                </Link>
              </NavigationMenuTrigger>

              <NavigationMenuContent>
                <ul className="p-6 w-72 rounded-lg shadow-sm">
                  {profession.subcategories &&
                  profession.subcategories.length > 0 ? (
                    profession.subcategories.map((sub, index) => (
                      <li
                        key={sub._id}
                        className={`${index !== 0 ? "mt-6" : ""}`}
                      >
                        <div className="border-b border-gray-100 pb-2 mb-3">
                          <h2 className="font-semibold text-base tracking-tight">
                            {sub.title}
                          </h2>
                        </div>
                        <ul className="space-y-2">
                          {sub.items && sub.items.length > 0 ? (
                            sub.items.map((item) => (
                              <li key={item._id}>
                                <Link
                                  to={`/${profession.professionTitle}/${sub.title}/${item.name}`}
                                  className="block px-3 py-2 text-sm  rounded-md transition-colors duration-150 ease-in-out border-l-2 border-transparent hover:border-blue-500"
                                >
                                  {item.name}
                                </Link>
                              </li>
                            ))
                          ) : (
                            <li className="px-3 py-2 text-sm text-gray-400 italic">
                              No items available
                            </li>
                          )}
                        </ul>
                      </li>
                    ))
                  ) : (
                    <li className="text-center py-8 text-gray-400 italic">
                      No subcategories available
                    </li>
                  )}
                </ul>
              </NavigationMenuContent>
            </NavigationMenuItem>
          ))
        )}
      </NavigationMenuList>
    </NavigationMenu>
  );
}
