import { useEffect, useState } from "react";
import axios from "axios";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import { Link } from "react-router-dom";

export function Navbar() {
  const [professions, setProfessions] = useState([]);

  useEffect(() => {
    const fetchProfessions = async () => {
      try {
        const res = await axios.get("http://localhost:3000/api/admin/professions");
        setProfessions(res.data.data); // make sure the response is shaped as expected
      } catch (error) {
        console.error("Error fetching professions:", error);
      }
    };

    fetchProfessions();
  }, []);

  return (
    <NavigationMenu viewport={false}>
      <NavigationMenuList>
        {professions.map((profession) => (
          <NavigationMenuItem key={profession._id}>
            <NavigationMenuTrigger>
              <Link to={`/profession/${profession._id}`}>
                {profession.professionTitle}
              </Link>
            </NavigationMenuTrigger>

            <NavigationMenuContent>
              <ul className="p-4 w-64">
                {profession.subcategories.map((sub) => (
                  <li key={sub._id} className="mb-4">
                    <h2 className="font-semibold text-sm mb-1">{sub.title}</h2>
                    <ul className="ml-2 list-disc text-xs text-muted-foreground">
                      {sub.items.map((item) => (
                        <li key={item._id}>
                          <Link
                            to={`/${profession.title}/${sub.title}/${sub.item.name}`}
                            className="hover:underline"
                          >
                            {item.name}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </li>
                ))}
              </ul>
            </NavigationMenuContent>
          </NavigationMenuItem>
        ))}
      </NavigationMenuList>
    </NavigationMenu>
  );
}
