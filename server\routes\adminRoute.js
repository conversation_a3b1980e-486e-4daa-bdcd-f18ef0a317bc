const express = require("express");
const router = express.Router();
const { addProfession, getProfessions } = require("../controllers/adminController");
const verifyAdminMiddleware = require("../middlewares/verifyAdminMiddleware");

router.post("/professions", verifyAdminMiddleware, addProfession);
router.get("/professions", getProfessions); // Remove middleware for public access

module.exports = router;
